import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { usePlaylistStore } from "@/stores/usePlaylistStore";
import { usePlayerStore } from "@/stores/usePlayerStore";
import { useEngagementStore } from "@/stores/useEngagementStore";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import { LikeButton } from "@/components/ui/LikeButton";
import { AlbumName } from "@/components/ui/AlbumName";
import { Play, Pause, Clock, Music } from "lucide-react";
import { Song } from "@/types";

const PlaylistPage = () => {
  const { playlistId } = useParams<{ playlistId: string }>();
  const { currentPlaylist, fetchPlaylistById, isLoading } = usePlaylistStore();
  const { currentSong, isPlaying, setCurrentSong, togglePlay, playAlbum } = usePlayerStore();
  const { trackSongPlay } = useEngagementStore();
  const [viewMode, setViewMode] = useState<"mobile" | "tablet" | "desktop">("desktop");

  useEffect(() => {
    if (playlistId) {
      fetchPlaylistById(playlistId);
    }

    const checkViewMode = () => {
      const width = window.innerWidth;
      if (width < 768) {
        setViewMode("mobile");
      } else if (width < 1024) {
        setViewMode("tablet");
      } else {
        setViewMode("desktop");
      }
    };

    checkViewMode();
    window.addEventListener("resize", checkViewMode);
    return () => window.removeEventListener("resize", checkViewMode);
  }, [playlistId, fetchPlaylistById]);

  const handlePlay = (song: Song, index: number) => {
    if (currentSong?._id === song._id) {
      togglePlay();
    } else {
      setCurrentSong(song);
      trackSongPlay(song._id);
    }
  };

  const handlePlayAll = () => {
    if (currentPlaylist?.songs && currentPlaylist.songs.length > 0) {
      playAlbum(currentPlaylist.songs, 0);
    }
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">Loading playlist...</p>
        </div>
      </div>
    );
  }

  if (!currentPlaylist) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <div className="w-24 h-24 rounded-full bg-gradient-to-br from-gray-400 to-gray-600 flex items-center justify-center mx-auto mb-6">
            <Music className="w-12 h-12 text-white" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Playlist not found
          </h2>
          <p className="text-gray-600 mb-6">
            The playlist you're looking for doesn't exist or you don't have access to it.
          </p>
          <Button asChild>
            <Link to="/">Go Home</Link>
          </Button>
        </div>
      </div>
    );
  }

  if (currentPlaylist.songs.length === 0) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <div className="w-24 h-24 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mx-auto mb-6">
            <Music className="w-12 h-12 text-white" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            No songs in this playlist
          </h2>
          <p className="text-gray-600 mb-6">
            This playlist is empty. Add some songs to get started!
          </p>
          <Button asChild>
            <Link to="/">Discover Music</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-white mt-5 md:mt-0">
      <ScrollArea className="h-full">
        <div className="p-6 space-y-6">
          {/* Header */}
          <div className="flex items-end gap-6">
            <div className="w-48 h-48 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center shadow-xl">
              {currentPlaylist.imageUrl || currentPlaylist.songs[0]?.imageUrl ? (
                <img
                  src={currentPlaylist.imageUrl || currentPlaylist.songs[0]?.imageUrl}
                  alt={currentPlaylist.name}
                  className="w-full h-full object-cover rounded-lg"
                />
              ) : (
                <Music className="w-24 h-24 text-white" />
              )}
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600 mb-2">PLAYLIST</p>
              <h1 className="text-5xl font-bold text-gray-900 mb-4">
                {currentPlaylist.name}
              </h1>
              {currentPlaylist.description && (
                <p className="text-gray-600 mb-2">{currentPlaylist.description}</p>
              )}
              <p className="text-gray-600 mb-4">
                {currentPlaylist.songs.length} song{currentPlaylist.songs.length !== 1 ? 's' : ''}
              </p>
              <Button
                onClick={handlePlayAll}
                size="lg"
                className="px-8"
                style={{ backgroundColor: "#D9AD39" }}
              >
                <Play className="w-5 h-5 mr-2" />
                Play all
              </Button>
            </div>
          </div>

          {/* Songs List */}
          <div className="space-y-1">
            {/* Header Row */}
            <div className="grid grid-cols-12 gap-4 px-4 py-2 text-sm font-medium text-gray-500 border-b border-gray-200">
              <div className="col-span-1 text-center">#</div>
              <div className="col-span-6">TITLE</div>
              <div className="col-span-3">ALBUM</div>
              <div className="col-span-1 text-center">
                <Clock className="w-4 h-4 mx-auto" />
              </div>
              <div className="col-span-1"></div>
            </div>

            {/* Songs */}
            {currentPlaylist.songs.map((song, index) => {
              const isCurrentSong = currentSong?._id === song._id;
              const isCurrentlyPlaying = isCurrentSong && isPlaying;

              return (
                <div
                  key={song._id}
                  className="grid grid-cols-12 gap-4 px-4 py-3 rounded-lg hover:bg-gray-50 group cursor-pointer transition-colors"
                  onClick={() => handlePlay(song, index)}
                >
                  <div className="col-span-1 flex items-center justify-center">
                    <div className="relative">
                      <span
                        className={`text-sm ${
                          isCurrentSong ? "text-[#D9AD39]" : "text-gray-500"
                        } group-hover:hidden`}
                      >
                        {index + 1}
                      </span>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="w-8 h-8 hidden group-hover:flex absolute -top-2 -left-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          handlePlay(song, index);
                        }}
                      >
                        {isCurrentlyPlaying ? (
                          <Pause className="w-4 h-4" />
                        ) : (
                          <Play className="w-4 h-4" />
                        )}
                      </Button>
                    </div>
                  </div>

                  <div className="col-span-6 flex items-center gap-3">
                    <img
                      src={song.imageUrl}
                      alt={song.title}
                      className="w-10 h-10 rounded object-cover"
                    />
                    <div className="min-w-0">
                      <p
                        className={`font-medium truncate hover:underline hover:text-primary ${
                          isCurrentSong ? "text-[#D9AD39]" : "text-gray-900"
                        }`}
                      >
                        <Link to={`/song/${song._id}`}>
                          {song.title}
                        </Link>
                      </p>
                      <p className="text-sm text-gray-600 truncate hover:underline hover:text-primary">
                        <Link to={`/artist/${song.artistId}`}>
                        {song.artist}
                        </Link>
                      </p>
                    </div>
                  </div>

                  <div className="col-span-3 flex items-center">
                    <p className="text-sm text-gray-600 truncate">
                      <AlbumName albumId={song.albumId} />
                    </p>
                  </div>

                  <div className="col-span-1 flex items-center justify-center">
                    <span className="text-sm text-gray-500">
                      {formatDuration(song.duration)}
                    </span>
                  </div>

                  <div className="col-span-1 flex items-center justify-center">
                    <LikeButton
                      songId={song._id}
                      size="sm"
                      variant="ghost"
                      count={song.likeCount || 0}
                    />
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </ScrollArea>
    </div>
  );
};

export default PlaylistPage;