import { axiosInstance } from "@/lib/axios";
import { Album, Song, Stats, Artist } from "@/types";
import toast from "react-hot-toast";
import { create } from "zustand";

interface MusicStore {
	songs: Song[];
	albums: Album[];
	artists: Artist[];
	popularArtists: Artist[];
	isLoading: boolean;
	error: string | null;
	currentAlbum: Album | null;
	featuredSongs: Song[];
	madeForYouSongs: Song[];
	trendingSongs: Song[];
	currentSong: Song | null;
	currentArtist: Artist | null;
	stats: Stats;

	fetchAlbums: () => Promise<void>;
	fetchAlbumById: (id: string) => Promise<void>;
	fetchAlbumByIdNoSet: (id: string) => Promise<Album>;
	fetchFeaturedSongs: () => Promise<void>;
	fetchMadeForYouSongs: () => Promise<void>;
	fetchTrendingSongs: () => Promise<void>;
	fetchPopularArtists: () => Promise<void>;
	fetchArtistById: (id: string) => Promise<void>;
	fetchCurrentArtsist: (id: string) => Promise<void>;
	fetchStats: () => Promise<void>;
	fetchSongs: () => Promise<void>;
	fetchSongById: (id: string) => Promise<void>;
	deleteSong: (id: string) => Promise<void>;
	deleteAlbum: (id: string) => Promise<void>;
	fetchArtists: () => Promise<void>;
	deleteArtist: (id: string) => Promise<void>;
	addArtist: (formData: FormData) => Promise<void>;
	addSong: (formData: FormData) => Promise<void>;
	addAlbum: (formData: FormData) => Promise<void>;
	editSong: (id: string, formData: FormData) => Promise<void>;
	editAlbum: (id: string, formData: FormData) => Promise<void>;
	editArtist: (id: string, formData: FormData) => Promise<void>;
	bulkUploadSongs: (songs: FormData[]) => Promise<void>;
	bulkUploadAlbums: (albums: FormData[]) => Promise<void>;
	bulkUploadArtists: (artists: FormData[]) => Promise<void>;
}

export const useMusicStore = create<MusicStore>((set) => ({
	albums: [],
	songs: [],
	artists: [],
	popularArtists: [],
	isLoading: false,
	error: null,
	currentAlbum: null,
	madeForYouSongs: [],
	featuredSongs: [],
	trendingSongs: [],
	stats: {
		totalSongs: 0,
		totalAlbums: 0,
		totalUsers: 0,
		totalArtists: 0,
	},
	currentSong: null,
	currentArtist: null,

	fetchArtistById: async (id) => {
		set({ isLoading: true, error: null });
		try {
			const response = await axiosInstance.get(`/artists/${id}`);
			return response.data
		} catch (error: any) {
			set({ error: error.response.data.message });
		} finally {
			set({ isLoading: false });
		}
	},

	fetchCurrentArtsist: async (id) => {
		set({ isLoading: true, error: null });
		try {
			const response = await axiosInstance.get(`/artists/${id}`);
			set({ currentArtist: response.data });
		} catch (error: any) {
			set({ error: error.response.data.message });
		} finally {
			set({ isLoading: false });
		}
	},

	fetchSongById: async (id) => {
		set({ isLoading: true, error: null });
		try {
			const response = await axiosInstance.get(`/songs/${id}`);
			set({ currentSong: response.data });
		} catch (error: any) {
			set({ error: error.response.data.message });
		} finally {
			set({ isLoading: false });
		}
	},

	deleteSong: async (id) => {
		set({ isLoading: true, error: null });
		try {
			await axiosInstance.delete(`/admin/songs/${id}`);

			set((state) => ({
				songs: state.songs.filter((song) => song._id !== id),
			}));
			toast.success("Song deleted successfully");
		} catch (error: any) {
			console.log("Error in deleteSong", error);
			toast.error("Error deleting song");
		} finally {
			set({ isLoading: false });
		}
	},

	deleteAlbum: async (id) => {
		set({ isLoading: true, error: null });
		try {
			await axiosInstance.delete(`/admin/albums/${id}`);
			set((state) => ({
				albums: state.albums.filter((album) => album._id !== id),
				songs: state.songs.map((song) =>
					song.albumId === state.albums.find((a) => a._id === id)?.title ? { ...song, album: null } : song
				),
			}));
			toast.success("Album deleted successfully");
		} catch (error: any) {
			toast.error("Failed to delete album: " + error.message);
		} finally {
			set({ isLoading: false });
		}
	},

	fetchSongs: async () => {
		set({ isLoading: true, error: null });
		try {
			const response = await axiosInstance.get("/songs");
			set({ songs: response.data });
		} catch (error: any) {
			set({ error: error.message });
		} finally {
			set({ isLoading: false });
		}
	},

	fetchStats: async () => {
		set({ isLoading: true, error: null });
		try {
			const response = await axiosInstance.get("/stats");
			set({ stats: response.data });
		} catch (error: any) {
			set({ error: error.message });
		} finally {
			set({ isLoading: false });
		}
	},

	fetchAlbums: async () => {
		set({ isLoading: true, error: null });

		try {
			const response = await axiosInstance.get("/albums");
			set({ albums: response.data });
		} catch (error: any) {
			set({ error: error.response.data.message });
		} finally {
			set({ isLoading: false });
		}
	},

	fetchAlbumById: async (id) => {
		set({ isLoading: true, error: null });
		try {
			const response = await axiosInstance.get(`/albums/${id}`);
			set({ currentAlbum: response.data });
		} catch (error: any) {
			set({ error: error.response.data.message });
		} finally {
			set({ isLoading: false });
		}
	},

	fetchAlbumByIdNoSet: async (id: string) => {
		set({ isLoading: true, error: null });
		try {
			const response = await axiosInstance.get(`/albums/${id}`);
			return response.data;
		} catch (error: any) {
			set({ error: error.response.data.message });
		} finally {
			set({ isLoading: false });
		}
	},

	fetchFeaturedSongs: async () => {
		set({ isLoading: true, error: null });
		try {
			const response = await axiosInstance.get("/songs/featured");
			set({ featuredSongs: response.data });
		} catch (error: any) {
			set({ error: error.response.data.message });
		} finally {
			set({ isLoading: false });
		}
	},

	fetchMadeForYouSongs: async () => {
		set({ isLoading: true, error: null });
		try {
			const response = await axiosInstance.get("/songs/made-for-you");
			set({ madeForYouSongs: response.data });
		} catch (error: any) {
			set({ error: error.response.data.message });
		} finally {
			set({ isLoading: false });
		}
	},

	fetchTrendingSongs: async () => {
		set({ isLoading: true, error: null });
		try {
			const response = await axiosInstance.get("/songs/trending");
			set({ trendingSongs: response.data });
		} catch (error: any) {
			set({ error: error.response.data.message });
		} finally {
			set({ isLoading: false });
		}
	},

	fetchPopularArtists: async () => {
		set({ isLoading: true, error: null });
		try {
			const response = await axiosInstance.get("/engagement/popular-artists");
			set({ popularArtists: response.data });
		} catch (error: any) {
			set({ error: error.response?.data?.message || error.message });
		} finally {
			set({ isLoading: false });
		}
	},

	fetchArtists: async () => {
		set({ isLoading: true, error: null });
		try {
			const response = await axiosInstance.get("/artists");
			// console.log("Fetched artists:", response.data);
			set({ artists: response.data });
		} catch (error: any) {
			set({ error: error.response.data.message });
		} finally {
			set({ isLoading: false });
		}
	},

	deleteArtist: async (id) => {
		set({ isLoading: true, error: null });
		try {
			await axiosInstance.delete(`/admin/artists/${id}`);
			set((state) => ({
				artists: state.artists.filter((artist) => artist._id !== id),
			}));
			toast.success("Artist deleted successfully");
		} catch (error: any) {
			toast.error("Failed to delete artist: " + error.message);
		} finally {
			set({ isLoading: false });
		}
	},

	addArtist: async (formData) => {
		set({ isLoading: true, error: null });
		try {
			const response = await axiosInstance.post("/admin/artists", formData, {
				headers: {
					"Content-Type": "multipart/form-data",
				},
			});
			
			// Optimistically add the new artist to the list
			set((state) => ({
				artists: [response.data, ...state.artists],
			}));
			
			toast.success("Artist created successfully");
		} catch (error: any) {
			toast.error("Failed to create artist: " + error.message);
		} finally {
			set({ isLoading: false });
		}
	},

	addSong: async (formData) => {
		set({ isLoading: true, error: null });
		
		// Create optimistic song object
		const optimisticSong: Song = {
			_id: `temp-${Date.now()}`, // Temporary ID
			title: formData.get("title") as string,
			artist: formData.get("artist") as string,
			albumId: formData.get("albumId") as string || null,
			artistId: formData.get("artistId") as string || null,
			featuredArtists: formData.get("featuredArtists") ? (formData.get("featuredArtists") as string).split(',').map(a => a.trim()) : [],
			imageUrl: "", // Will be filled after upload
			audioUrl: "", // Will be filled after upload
			duration: parseInt(formData.get("duration") as string) || 0,
			releaseDate: formData.get("releaseDate") as string || new Date().toISOString(),
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString(),
		};

		// Optimistically add the song
		set((state) => ({
			songs: [optimisticSong, ...state.songs],
		}));

		try {
			const response = await axiosInstance.post("/admin/songs", formData, {
				headers: {
					"Content-Type": "multipart/form-data",
				},
			});
			
			// Replace optimistic song with real song
			set((state) => ({
				songs: state.songs.map(song => 
					song._id === optimisticSong._id ? response.data : song
				),
			}));
			
			toast.success("Song added successfully");
		} catch (error: any) {
			// Remove optimistic song on error
			set((state) => ({
				songs: state.songs.filter(song => song._id !== optimisticSong._id),
			}));
			toast.error("Failed to add song: " + error.message);
		} finally {
			set({ isLoading: false });
		}
	},

	addAlbum: async (formData) => {
		set({ isLoading: true, error: null });
		
		// Create optimistic album object
		const optimisticAlbum: Album = {
			_id: `temp-${Date.now()}`, // Temporary ID
			title: formData.get("title") as string,
			artist: formData.get("artist") as string,
			artistId: formData.get("artistId") as string || null,
			imageUrl: "", // Will be filled after upload
			releaseYear: parseInt(formData.get("releaseYear") as string) || new Date().getFullYear(),
			genre: formData.get("genre") as string,
			songs: [],
		};

		// Optimistically add the album
		set((state) => ({
			albums: [optimisticAlbum, ...state.albums],
		}));

		try {
			const response = await axiosInstance.post("/admin/albums", formData, {
				headers: {
					"Content-Type": "multipart/form-data",
				},
			});
			
			// Replace optimistic album with real album
			set((state) => ({
				albums: state.albums.map(album => 
					album._id === optimisticAlbum._id ? response.data : album
				),
			}));
			
			toast.success("Album created successfully");
		} catch (error: any) {
			// Remove optimistic album on error
			set((state) => ({
				albums: state.albums.filter(album => album._id !== optimisticAlbum._id),
			}));
			toast.error("Failed to create album: " + error.message);
		} finally {
			set({ isLoading: false });
		}
	},

	editSong: async (id, formData) => {
		set({ isLoading: true, error: null });
		
		// Store original song for rollback
		let originalSong: Song | undefined;
		
		// Optimistically update the song
		set((state) => {
			originalSong = state.songs.find(song => song._id === id);
			if (!originalSong) return state;
			
			const updatedSong: Song = {
				...originalSong,
				title: formData.get("title") as string || originalSong.title,
				artist: formData.get("artist") as string || originalSong.artist,
				albumId: formData.get("albumId") as string || originalSong.albumId,
				artistId: formData.get("artistId") as string || originalSong.artistId,
				featuredArtists: formData.get("featuredArtists") ? 
					(formData.get("featuredArtists") as string).split(',').map(a => a.trim()) : 
					originalSong.featuredArtists,
				duration: parseInt(formData.get("duration") as string) || originalSong.duration,
				releaseDate: formData.get("releaseDate") as string || originalSong.releaseDate,
				updatedAt: new Date().toISOString(),
			};
			
			return {
				songs: state.songs.map(song => 
					song._id === id ? updatedSong : song
				),
			};
		});

		try {
			const response = await axiosInstance.put(`/admin/songs/${id}`, formData, {
				headers: {
					"Content-Type": "multipart/form-data",
				},
			});
			
			// Update with actual response data
			set((state) => ({
				songs: state.songs.map(song => 
					song._id === id ? response.data : song
				),
			}));
			
			toast.success("Song updated successfully");
		} catch (error: any) {
			// Rollback on error
			if (originalSong) {
				set((state) => ({
					songs: state.songs.map(song => 
						song._id === id ? originalSong! : song
					),
				}));
			}
			toast.error("Failed to update song: " + error.message);
		} finally {
			set({ isLoading: false });
		}
	},

	editAlbum: async (id, formData) => {
		set({ isLoading: true, error: null });
		
		// Store original album for rollback
		let originalAlbum: Album | undefined;
		
		// Optimistically update the album
		set((state) => {
			originalAlbum = state.albums.find(album => album._id === id);
			if (!originalAlbum) return state;
			
			const updatedAlbum: Album = {
				...originalAlbum,
				title: formData.get("title") as string || originalAlbum.title,
				artist: formData.get("artist") as string || originalAlbum.artist,
				artistId: formData.get("artistId") as string || originalAlbum.artistId,
				releaseYear: parseInt(formData.get("releaseYear") as string) || originalAlbum.releaseYear,
				genre: formData.get("genre") as string || originalAlbum.genre,
			};
			
			return {
				albums: state.albums.map(album => 
					album._id === id ? updatedAlbum : album
				),
			};
		});

		try {
			const response = await axiosInstance.put(`/admin/albums/${id}`, formData, {
				headers: {
					"Content-Type": "multipart/form-data",
				},
			});
			
			// Update with actual response data
			set((state) => ({
				albums: state.albums.map(album => 
					album._id === id ? response.data : album
				),
			}));
			
			toast.success("Album updated successfully");
		} catch (error: any) {
			// Rollback on error
			if (originalAlbum) {
				set((state) => ({
					albums: state.albums.map(album => 
						album._id === id ? originalAlbum! : album
					),
				}));
			}
			toast.error("Failed to update album: " + error.message);
		} finally {
			set({ isLoading: false });
		}
	},

	editArtist: async (id, formData) => {
		set({ isLoading: true, error: null });
		
		// Store original artist for rollback
		let originalArtist: Artist | undefined;
		
		// Optimistically update the artist
		set((state) => {
			originalArtist = state.artists.find(artist => artist._id === id);
			if (!originalArtist) return state;
			
			const updatedArtist: Artist = {
				...originalArtist,
				name: formData.get("name") as string || originalArtist.name,
				bgColor: formData.get("bgColor") as string || originalArtist.bgColor,
				updatedAt: new Date().toISOString(),
			};
			
			return {
				artists: state.artists.map(artist => 
					artist._id === id ? updatedArtist : artist
				),
			};
		});

		try {
			const response = await axiosInstance.put(`/admin/artists/${id}`, formData, {
				headers: {
					"Content-Type": "multipart/form-data",
				},
			});
			
			// Update with actual response data
			set((state) => ({
				artists: state.artists.map(artist => 
					artist._id === id ? response.data : artist
				),
			}));
			
			toast.success("Artist updated successfully");
		} catch (error: any) {
			// Rollback on error
			if (originalArtist) {
				set((state) => ({
					artists: state.artists.map(artist => 
						artist._id === id ? originalArtist! : artist
					),
				}));
			}
			toast.error("Failed to update artist: " + error.message);
		} finally {
			set({ isLoading: false });
		}
	},

	bulkUploadSongs: async (songs) => {
		set({ isLoading: true, error: null });
		
		// Create optimistic songs
		const optimisticSongs = songs.map((formData, index) => ({
			_id: `temp-song-${Date.now()}-${index}`,
			title: formData.get("title") as string,
			artist: formData.get("artist") as string,
			albumId: formData.get("albumId") as string || null,
			artistId: formData.get("artistId") as string || null,
			featuredArtists: formData.get("featuredArtists") ? (formData.get("featuredArtists") as string).split(',').map(a => a.trim()) : [],
			imageUrl: "",
			audioUrl: "",
			duration: parseInt(formData.get("duration") as string) || 0,
			releaseDate: formData.get("releaseDate") as string || new Date().toISOString(),
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString(),
		}));

		// Optimistically add all songs
		set((state) => ({
			songs: [...optimisticSongs, ...state.songs],
		}));

		try {
			const promises = songs.map(formData => 
				axiosInstance.post("/admin/songs", formData, {
					headers: {
						"Content-Type": "multipart/form-data",
					},
				})
			);

			const responses = await Promise.all(promises);
			
			// Replace optimistic songs with real songs
			set((state) => ({
				songs: state.songs.map(song => {
					const tempIndex = optimisticSongs.findIndex(temp => temp._id === song._id);
					if (tempIndex !== -1) {
						return responses[tempIndex].data;
					}
					return song;
				}),
			}));
			
			toast.success(`${songs.length} songs uploaded successfully`);
		} catch (error: any) {
			// Remove optimistic songs on error
			const tempIds = optimisticSongs.map(song => song._id);
			set((state) => ({
				songs: state.songs.filter(song => !tempIds.includes(song._id)),
			}));
			toast.error("Failed to bulk upload songs: " + error.message);
		} finally {
			set({ isLoading: false });
		}
	},

	bulkUploadAlbums: async (albums) => {
		set({ isLoading: true, error: null });
		
		// Create optimistic albums
		const optimisticAlbums = albums.map((formData, index) => ({
			_id: `temp-album-${Date.now()}-${index}`,
			title: formData.get("title") as string,
			artist: formData.get("artist") as string,
			artistId: formData.get("artistId") as string || null,
			imageUrl: "",
			releaseYear: parseInt(formData.get("releaseYear") as string) || new Date().getFullYear(),
			genre: formData.get("genre") as string,
			songs: [],
		}));

		// Optimistically add all albums
		set((state) => ({
			albums: [...optimisticAlbums, ...state.albums],
		}));

		try {
			const promises = albums.map(formData => 
				axiosInstance.post("/admin/albums", formData, {
					headers: {
						"Content-Type": "multipart/form-data",
					},
				})
			);

			const responses = await Promise.all(promises);
			
			// Replace optimistic albums with real albums
			set((state) => ({
				albums: state.albums.map(album => {
					const tempIndex = optimisticAlbums.findIndex(temp => temp._id === album._id);
					if (tempIndex !== -1) {
						return responses[tempIndex].data;
					}
					return album;
				}),
			}));
			
			toast.success(`${albums.length} albums uploaded successfully`);
		} catch (error: any) {
			// Remove optimistic albums on error
			const tempIds = optimisticAlbums.map(album => album._id);
			set((state) => ({
				albums: state.albums.filter(album => !tempIds.includes(album._id)),
			}));
			toast.error("Failed to bulk upload albums: " + error.message);
		} finally {
			set({ isLoading: false });
		}
	},

	bulkUploadArtists: async (artists) => {
		set({ isLoading: true, error: null });
		
		// Create optimistic artists
		const optimisticArtists = artists.map((formData, index) => ({
			_id: `temp-artist-${Date.now()}-${index}`,
			name: formData.get("name") as string,
			imageUrl: "",
			bgColor: formData.get("bgColor") as string || "#1f2937",
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString(),
		}));

		// Optimistically add all artists
		set((state) => ({
			artists: [...optimisticArtists, ...state.artists],
		}));

		try {
			const promises = artists.map(formData => 
				axiosInstance.post("/admin/artists", formData, {
					headers: {
						"Content-Type": "multipart/form-data",
					},
				})
			);

			const responses = await Promise.all(promises);
			
			// Replace optimistic artists with real artists
			set((state) => ({
				artists: state.artists.map(artist => {
					const tempIndex = optimisticArtists.findIndex(temp => temp._id === artist._id);
					if (tempIndex !== -1) {
						return responses[tempIndex].data;
					}
					return artist;
				}),
			}));
			
			toast.success(`${artists.length} artists uploaded successfully`);
		} catch (error: any) {
			// Remove optimistic artists on error
			const tempIds = optimisticArtists.map(artist => artist._id);
			set((state) => ({
				artists: state.artists.filter(artist => !tempIds.includes(artist._id)),
			}));
			toast.error("Failed to bulk upload artists: " + error.message);
		} finally {
			set({ isLoading: false });
		}
	},
}));
